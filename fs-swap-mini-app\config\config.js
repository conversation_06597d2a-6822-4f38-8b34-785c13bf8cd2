/**
 * 全局配置文件
 * 集中管理所有配置项，便于维护和切换环境
 */

// 判断当前环境
const ENV = {
  DEV: 'development',
  PROD: 'production',
  TEST: 'test'
};

// 当前环境，可以根据需要修改
const CURRENT_ENV = ENV.DEV;

// 各环境的配置
const ENV_CONFIG = {
  [ENV.DEV]: {
    BASE_URL: 'http://172.20.19.21:8081/wx',
  },
  [ENV.DEV1]: {
    BASE_URL: 'http://192.168.2.184:8081/wx',
  },
  [ENV.TEST]: {
    BASE_URL: 'http://test-api.example.com/wx',
  },
  [ENV.PROD]: {
    BASE_URL: 'https://ei-api.funnyswap.com/wx',
  }
};

// 导出当前环境的配置
module.exports = {
  // 基础URL
  BASE_URL: ENV_CONFIG[CURRENT_ENV].BASE_URL,

  // 存储键名
  STORAGE_KEYS: {
    TOKEN: 'token',
    USER_INFO: 'userInfo',
    SYSTEM_INFO: 'systemInfo',
    LOCAL_RESIDENTIAL: 'localResidential' // 本地选择的小区信息
  },

  // 请求配置
  REQUEST: {
    TIMEOUT: 10000,
    RETRY_COUNT: 2,
    RETRY_DELAY: 1000
  },

  // 错误消息
  MESSAGES: {
    UPDATE_TITLE: '更新提示',
    UPDATE_CONTENT: '新版本已经准备好，是否重启立即更新？',
    NETWORK_ERROR: '网络错误',
    ACCESS_ERROR: '访问异常',
    SESSION_EXPIRED: '登录已过期，请重新登录',
    LOGIN_REQUIRED: '请先登录',
    AUTH_DENIED: '授权失败，请重试',
    PHONE_AUTH_DENIED: '需要授权手机号才能继续操作',
  },

  // 小区认证状态
  RESIDENTIAL_CERTIFICATION_STATUS: {
    UNCERTIFIED: '0',    // 未认证
    PENDING: '1',        // 认证中
    CERTIFIED: '2'       // 已认证
  },

  // 客服配置
  CUSTOMER_SERVICE: {
    // 企业微信客服配置
    CORP_ID: 'wwbba7dc17f30425b3', // 企业ID，需要替换为实际的企业ID
    SERVICE_URL: 'https://work.weixin.qq.com/kfid/kfcd048a19afdaa5815', // 客服链接，需要替换为实际的客服链接
    // 消息卡片配置
    MESSAGE_CARD: {
      TITLE: '来自趣换闲置小程序的咨询',
      PATH: '/pages/user/personal/personal',
      IMAGE: '' // 小程序图标URL
    }
  }
};
