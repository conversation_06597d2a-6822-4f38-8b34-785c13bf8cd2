package com.fs.swap.system.service.impl;

import java.util.List;
import com.fs.swap.common.utils.DateUtils;
import com.fs.swap.common.core.domain.entity.CommunityHelp;
import com.fs.swap.system.mapper.CommunityHelpMapper;
import com.fs.swap.system.service.ICommunityHelpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 邻里互助Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
@Service
public class CommunityHelpServiceImpl implements ICommunityHelpService 
{
    @Autowired
    private CommunityHelpMapper communityHelpMapper;

    /**
     * 查询邻里互助
     * 
     * @param id 邻里互助主键
     * @return 邻里互助
     */
    @Override
    public CommunityHelp selectCommunityHelpById(Long id)
    {
        return communityHelpMapper.selectCommunityHelpById(id);
    }

    /**
     * 查询邻里互助列表
     * 
     * @param communityHelp 邻里互助
     * @return 邻里互助
     */
    @Override
    public List<CommunityHelp> selectCommunityHelpList(CommunityHelp communityHelp)
    {
        return communityHelpMapper.selectCommunityHelpList(communityHelp);
    }

    /**
     * 新增邻里互助
     * 
     * @param communityHelp 邻里互助
     * @return 结果
     */
    @Override
    public int insertCommunityHelp(CommunityHelp communityHelp)
    {
        communityHelp.setCreateTime(DateUtils.getNowDate());
        return communityHelpMapper.insertCommunityHelp(communityHelp);
    }

    /**
     * 修改邻里互助
     * 
     * @param communityHelp 邻里互助
     * @return 结果
     */
    @Override
    public int updateCommunityHelp(CommunityHelp communityHelp)
    {
        communityHelp.setUpdateTime(DateUtils.getNowDate());
        return communityHelpMapper.updateCommunityHelp(communityHelp);
    }

    /**
     * 批量删除邻里互助
     * 
     * @param ids 需要删除的邻里互助主键
     * @return 结果
     */
    @Override
    public int deleteCommunityHelpByIds(Long[] ids)
    {
        return communityHelpMapper.deleteCommunityHelpByIds(ids);
    }

    /**
     * 删除邻里互助信息
     * 
     * @param id 邻里互助主键
     * @return 结果
     */
    @Override
    public int deleteCommunityHelpById(Long id)
    {
        return communityHelpMapper.deleteCommunityHelpById(id);
    }

    /**
     * 增加浏览次数
     * 
     * @param id 邻里互助主键
     * @return 结果
     */
    @Override
    public int incrementViewCount(Long id)
    {
        return communityHelpMapper.incrementViewCount(id);
    }
}
