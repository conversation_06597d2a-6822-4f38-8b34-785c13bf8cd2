page {
  --primary-color: #ffffff;
  --secondary-color: #f8f9fa;
  --accent-color: #3B7FFF;
  --accent-gradient: linear-gradient(135deg, #3B7FFF, #2B6EF3);
  --text-color: #333333;
  --text-secondary: #666666;
  --text-light: #999999;
  --background-color: #f7f8fa;
  --card-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
  --hover-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.12);
  --blue-bg: linear-gradient(135deg, #3B7FFF, #2B6EF3);
  --green-bg: linear-gradient(135deg, #36CFC9, #32BEB8);
  --orange-bg: linear-gradient(135deg, #FF9B3F, #FF8326);
  --border-radius: 16rpx;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --border-color: #eeeeee;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4757;
  --info-color: #1890ff;
  --gray-50: #fafafa;
  --gray-100: #f5f5f5;
  --gray-200: #e8e8e8;
}

.container {
  background: linear-gradient(180deg, #f7f8fa 0%, #ffffff 100%);
  height: 100vh;
  width: 100%;
  box-sizing: border-box;
  -webkit-overflow-scrolling: touch;
  overflow-x: hidden;
}

/* 自定义导航栏样式 */
.custom-nav {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: var(--primary-color);
  z-index: 9999;
  width: 100%;
  box-sizing: border-box;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.custom-nav-content {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 30rpx;
  box-sizing: border-box;
  flex: 1;
  overflow: hidden;
  height: 100%;
}

/* 位置信息样式 */
.residential-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 100%;
  flex: 1;
  min-width: 0;
}

.life-text {
  font-size: 35rpx;
  color: var(--accent-color);
  font-weight: 600;
  letter-spacing: 1rpx;
}

.divider {
  margin: 0 10rpx;
  color: #cccccc;
  font-size: 24rpx;
}

.location-container {
  display: flex;
  align-items: center;
}

.residential-name {
  margin-left: 4rpx;
  font-size: 28rpx;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 220rpx;
}

/* 搜索框样式 */
.search-container {
  display: flex;
  align-items: center;
  margin-left: 16rpx;
  flex-shrink: 0;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 16rpx;
  padding: 0 16rpx;
  height: 52rpx;
  width: 240rpx;
  transition: all 0.3s ease;
}

.search-input-wrapper:focus-within {
  background-color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(59, 127, 255, 0.15);
  border: 1px solid var(--accent-color);
}

.search-icon {
  margin-right: 8rpx;
  flex-shrink: 0;
}

.search-input {
  flex: 1;
  font-size: 24rpx;
  color: var(--text-color);
  background: transparent;
  border: none;
  outline: none;
  height: 100%;
  line-height: 52rpx;
  padding: 0;
}

.search-placeholder {
  color: #999999;
  font-size: 24rpx;
}

.search-clear {
  position: absolute;
  right: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28rpx;
  height: 28rpx;
  border-radius: 50%;
  background-color: #e5e5e5;
  transition: all 0.2s ease;
}

.search-clear:active {
  background-color: #d9d9d9;
  transform: scale(0.9);
}

/* 商品列表样式 */
.product-list {
  padding: 0 24rpx;
  margin-top: 10rpx;
}

.product-card {
  margin-bottom: 24rpx !important;
  border-radius: 16rpx !important;
  overflow: hidden !important;
  background-color: #ffffff !important;
  box-shadow: var(--card-shadow) !important;
  transition: all 0.2s ease !important;
}

.product-card:active {
  transform: scale(0.98) !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05) !important;
}

.price-class {
  color: var(--accent-color) !important;
  font-weight: bold !important;
  font-size: 32rpx !important;
}

.title-class {
  font-size: 28rpx !important;
  font-weight: 600 !important;
  color: var(--text-color) !important;
  line-height: 1.3 !important;
  margin-bottom: 6rpx !important;
}

.desc-class {
  color: var(--text-light) !important;
  font-size: 24rpx !important;
  line-height: 1.4 !important;
}

.product-tag {
  margin-right: 10rpx;
  font-size: 20rpx !important;
  padding: 2rpx 12rpx !important;
  border-color: var(--accent-color) !important;
  color: var(--accent-color) !important;
  border-radius: 8rpx !important;
}

.product-tag-special {
  margin-right: 10rpx;
  font-size: 20rpx !important;
  padding: 2rpx 12rpx !important;
  background-color: var(--accent-color) !important;
  color: #ffffff !important;
  border: none !important;
  border-radius: 8rpx !important;
}

/* 底部提示样式 */
.bottom-hint {
  text-align: center;
  color: var(--text-light);
  font-size: 24rpx;
  padding: 30rpx 0;
  position: relative;
}

.bottom-hint text {
  position: relative;
  padding: 0 30rpx;
}

.bottom-hint text::before,
.bottom-hint text::after {
  content: "";
  position: absolute;
  top: 50%;
  width: 60rpx;
  height: 1px;
  background-color: #e0e0e0;
}

.bottom-hint text::before {
  left: -30rpx;
}

.bottom-hint text::after {
  right: -30rpx;
}

/* 隐藏所有滚动条 */
page::-webkit-scrollbar,
::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  background: transparent !important;
  display: none !important;
}

/* 隐藏更多元素的滚动条 */
.container::-webkit-scrollbar,
view::-webkit-scrollbar,
scroll-view::-webkit-scrollbar,
.swap-items-container::-webkit-scrollbar,
.swap-items::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* Firefox 浏览器隐藏滚动条 */
page {
  scrollbar-width: none;
}

/* 确保容器不显示滚动条 */
.container {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.swap-items-container {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.thumb-class {
  width: 160rpx !important;
  height: 160rpx !important;
  border-radius: 12rpx !important;
  overflow: hidden !important;
}

.add-cart-btn {
  font-size: 24rpx !important;
  padding: 0 20rpx !important;
  font-weight: 500 !important;
  background: var(--accent-color) !important;
  border: none !important;
}

/* 轮播广告区 */
.banner-swiper {
  width: calc(100% - 32rpx);
  height: 250rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 20rpx rgba(59, 127, 255, 0.12);
  position: relative;
  z-index: 1;
  background: #ffffff;
  margin: 16rpx 16rpx 16rpx 16rpx;
  border: 1rpx solid rgba(59, 127, 255, 0.08);
  box-sizing: border-box;
}

.banner-swiper::before {
  content: '';
  position: absolute;
  right: -20%;
  top: 0;
  width: 300rpx;
  height: 300rpx;
  background: radial-gradient(circle, rgba(59, 127, 255, 0.02) 0%, transparent 70%);
  border-radius: 50%;
  z-index: 0;
}

.banner-image {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
  transition: var(--transition);
  opacity: 0;
  animation: fadeIn 0.6s ease-out forwards;
}

/* 功能按钮包装器 */
.functions-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 16rpx 16rpx 16rpx;
  gap: 14rpx;
}

/* 功能按钮样式 */
.function-item {
  width: calc(33.33% - 10rpx);
  height: 130rpx;
  border-radius: 20rpx;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  color: #fff;
  padding: 20rpx 18rpx;
  box-sizing: border-box;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  overflow: hidden;
  transform: translateY(0);
  animation: slideInUp 0.6s ease-out forwards;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.function-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, transparent 50%, rgba(255, 255, 255, 0.08) 100%);
  z-index: 0;
  opacity: 0;
  transition: all 0.4s ease;
}

.function-item:hover {
  transform: translateY(-4rpx) scale(1.02);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.18);
}

.function-item:hover::before {
  opacity: 1;
}

.function-item:active {
  transform: translateY(1rpx) scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

/* 为常用电话功能添加专属主题 */
.phone-function {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  animation-delay: 0.1s;
}

.phone-function::after {
  content: '';
  position: absolute;
  top: -30rpx;
  right: -30rpx;
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  z-index: 1;
  animation: pulse-phone 3s ease-in-out infinite;
}

@keyframes pulse-phone {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.4;
  }
  50% {
    transform: scale(1.2) rotate(180deg);
    opacity: 0.7;
  }
}

/* 为周边推荐功能添加专属主题 */
.community-function {
  background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
  animation-delay: 0.2s;
}

.community-function::after {
  content: '';
  position: absolute;
  bottom: -25rpx;
  left: -25rpx;
  width: 70rpx;
  height: 70rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12rpx;
  transform: rotate(45deg);
  z-index: 1;
  animation: float-community 4s ease-in-out infinite;
}

@keyframes float-community {
  0%, 100% {
    transform: rotate(45deg) translateY(0);
    opacity: 0.5;
  }
  50% {
    transform: rotate(45deg) translateY(-8rpx);
    opacity: 0.8;
  }
}

.function-decoration {
  position: absolute;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50rpx;
  background: rgba(255, 255, 255, 0.1);
  bottom: -30rpx;
  right: -30rpx;
  z-index: 0;
  animation: rotate-decoration 8s linear infinite;
}

@keyframes rotate-decoration {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.function-icon, .function-title, .function-subtitle {
  position: relative;
  z-index: 2;
}

.borrow-function {
  animation-delay: 0.3s;
}

.function-icon {
  margin-right: 0;
  margin-bottom: 0;
  width: 56rpx;
  height: 56rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
}

.function-item:hover .function-icon {
  transform: scale(1.05);
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);
}

.community-function .function-icon {
  background: rgba(255, 255, 255, 0.35);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.5);
}

.community-function:hover .function-icon {
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.12);
}

/* 为邻里互助功能添加专属主题 */
.help-function {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  animation-delay: 0.3s;
}

.help-function::after {
  content: '';
  position: absolute;
  top: -20rpx;
  right: -20rpx;
  width: 60rpx;
  height: 60rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: float 3s ease-in-out infinite;
}

.help-function .function-icon {
  background: rgba(255, 255, 255, 0.35);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.5);
}

.help-function:hover .function-icon {
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.12);
}

.function-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  text-align: center;
  height: 100%;
}

.function-title {
  font-size: 28rpx;
  font-weight: 700;
  margin-bottom: 4rpx;
  letter-spacing: 0.5rpx;
  color: #2c3e50;
  line-height: 1.2;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.3);
}

.function-subtitle {
  font-size: 22rpx;
  color: #34495e;
  letter-spacing: 0.5rpx;
  line-height: 1.3;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.2);
  opacity: 0.8;
}

/* 碳豆记录区 */
.carbon-bean-area {
  margin: 0 16rpx 16rpx 16rpx;
  background: linear-gradient(135deg, #ffffff 0%, #fafbff 50%, #ffffff 100%);
  border-radius: 24rpx;
  padding: 12rpx 20rpx 22rpx;
  box-shadow: 0 8rpx 32rpx rgba(59, 127, 255, 0.08), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(59, 127, 255, 0.12);
  position: relative;
  overflow: hidden;
  animation: slideInUp 0.6s ease-out forwards;
  animation-delay: 0.4s;
  opacity: 0;
  backdrop-filter: blur(20rpx);
}

/* 添加高级背景装饰 */
.carbon-bean-area::before {
  content: "";
  position: absolute;
  top: -40%;
  right: -15%;
  width: 240rpx;
  height: 240rpx;
  background: radial-gradient(circle, rgba(59, 127, 255, 0.06) 0%, rgba(59, 127, 255, 0.02) 40%, transparent 70%);
  border-radius: 50%;
  z-index: 0;
  animation: float 8s ease-in-out infinite;
}

.carbon-bean-area::after {
  content: "";
  position: absolute;
  bottom: -35%;
  left: -12%;
  width: 180rpx;
  height: 180rpx;
  background: radial-gradient(circle, rgba(54, 207, 201, 0.04) 0%, rgba(54, 207, 201, 0.01) 50%, transparent 80%);
  border-radius: 50%;
  z-index: 0;
  animation: float 10s ease-in-out infinite reverse;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg) scale(1);
  }
  33% {
    transform: translateY(-15rpx) rotate(120deg) scale(1.05);
  }
  66% {
    transform: translateY(-8rpx) rotate(240deg) scale(0.95);
  }
}

.carbon-bean-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 12rpx;
  position: relative;
  z-index: 1;
  border-bottom: 1rpx solid rgba(59, 127, 255, 0.08);
}

.carbon-title {
  font-size: 28rpx;
  font-weight: 700;
  color: #1a1a1a;
  letter-spacing: 0.5rpx;
  background: linear-gradient(135deg, #1a1a1a 0%, #333333 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
}

.carbon-title::before {
  content: "🏆";
  position: absolute;
  left: -36rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 28rpx;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(-50%);
  }
  40% {
    transform: translateY(-55%);
  }
  60% {
    transform: translateY(-52%);
  }
}

.carbon-more {
  font-size: 24rpx;
  color: var(--accent-color);
  font-weight: 600;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  background: transparent;
  transition: all 0.3s ease;
}

.carbon-more:active {
  background: rgba(59, 127, 255, 0.08);
  transform: scale(0.96);
}

.carbon-bean-list {
  display: flex;
  justify-content: space-around;
  position: relative;
  z-index: 1;
  gap: 12rpx;
  align-items: flex-end;
  padding: 15rpx 8rpx 0;
  perspective: 1000rpx;
}

.carbon-user {
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  padding: 20rpx 12rpx 16rpx;
  border-radius: 20rpx;
  position: relative;
  flex: 1;
  max-width: 160rpx;
  backdrop-filter: blur(20rpx);
}

/* 第一名样式 - 金牌冠军 */
.carbon-user.rank-first {
  background: linear-gradient(135deg, #fff9e6 0%, #fff1cc 50%, #ffe999 100%);
  transform: scale(1.05) translateY(-5rpx);
  box-shadow:
    0 10rpx 30rpx rgba(255, 193, 7, 0.25),
    0 3rpx 12rpx rgba(255, 193, 7, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  border: 2rpx solid #ffd54f;
  z-index: 4;
  padding: 24rpx 14rpx 18rpx;
  margin-bottom: 8rpx;
}

.carbon-user.rank-first::before {
  content: '';
  position: absolute;
  top: -6rpx;
  left: -6rpx;
  right: -6rpx;
  bottom: -6rpx;
  background: linear-gradient(45deg, #ffe082, #fff3c4, #ffe082, #fff9e6);
  border-radius: 28rpx;
  z-index: -1;
  opacity: 0.6;
}



.carbon-user.rank-first .user-avatar-container {
  width: 64rpx;
  height: 64rpx;
  position: relative;
}

.carbon-user.rank-first .user-avatar-container::after {
  content: '';
  position: absolute;
  top: -3rpx;
  left: -3rpx;
  right: -3rpx;
  bottom: -3rpx;
  border: 2rpx solid #ffd54f;
  border-radius: 50%;
  opacity: 0.5;
}

.carbon-user.rank-first .user-avatar {
  border: 3rpx solid #ffca28;
  box-shadow: 
    0 0 20rpx rgba(255, 202, 40, 0.4),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3);
}

.carbon-user.rank-first .user-name {
  color: #e65100;
  font-weight: 700;
  font-size: 24rpx;
  text-shadow: 0 2rpx 4rpx rgba(255, 255, 255, 0.8);
  letter-spacing: 0.5rpx;
}

.carbon-user.rank-first .user-reward {
  background: linear-gradient(135deg, #ff8f00 0%, #ffa726 50%, #ffb74d 100%);
  box-shadow: 
    0 5rpx 16rpx rgba(255, 143, 0, 0.35),
    inset 0 1rpx 2rpx rgba(255, 255, 255, 0.5);
  color: #ffffff;
  border: 2rpx solid #ffb74d;
  font-size: 20rpx;
  font-weight: 700;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

/* 第二名样式 - 银牌亚军 */
.carbon-user.rank-second {
  background: linear-gradient(135deg, #f5f5f5 0%, #eeeeee 50%, #e0e0e0 100%);
  transform: scale(1.02) translateY(5rpx);
  box-shadow:
    0 6rpx 24rpx rgba(96, 125, 139, 0.2),
    0 2rpx 8rpx rgba(96, 125, 139, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9);
  border: 2rpx solid #b0bec5;
  z-index: 3;
  padding: 20rpx 14rpx 16rpx;
  margin-bottom: 12rpx;
}

.carbon-user.rank-second::before {
  content: '';
  position: absolute;
  top: -4rpx;
  left: -4rpx;
  right: -4rpx;
  bottom: -4rpx;
  background: linear-gradient(45deg, #e0e0e0, #f5f5f5, #e0e0e0);
  border-radius: 26rpx;
  z-index: -1;
  opacity: 0.5;
}

.carbon-user.rank-second .user-avatar-container {
  width: 56rpx;
  height: 56rpx;
}

.carbon-user.rank-second .user-avatar {
  border: 2rpx solid #b0bec5;
  box-shadow: 
    0 0 15rpx rgba(176, 190, 197, 0.4),
    inset 0 1rpx 3rpx rgba(255, 255, 255, 0.6);
}

.carbon-user.rank-second .user-name {
  color: #424242;
  font-weight: 600;
  font-size: 22rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.carbon-user.rank-second .user-reward {
  background: linear-gradient(135deg, #607d8b 0%, #90a4ae 50%, #b0bec5 100%);
  box-shadow: 
    0 4rpx 12rpx rgba(96, 125, 139, 0.3),
    inset 0 1rpx 2rpx rgba(255, 255, 255, 0.4);
  color: #ffffff;
  border: 2rpx solid #b0bec5;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
  font-size: 18rpx;
}

/* 第三名样式 - 铜牌季军 */
.carbon-user.rank-third {
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 50%, #ffcc80 100%);
  transform: scale(1.02) translateY(5rpx);
  box-shadow:
    0 6rpx 24rpx rgba(255, 152, 0, 0.2),
    0 2rpx 8rpx rgba(255, 152, 0, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.7);
  border: 2rpx solid #ff9800;
  z-index: 2;
  padding: 20rpx 14rpx 16rpx;
  margin-bottom: 12rpx;
}

.carbon-user.rank-third::before {
  content: '';
  position: absolute;
  top: -4rpx;
  left: -4rpx;
  right: -4rpx;
  bottom: -4rpx;
  background: linear-gradient(45deg, #ffcc80, #fff3e0, #ffcc80);
  border-radius: 26rpx;
  z-index: -1;
  opacity: 0.5;
}

.carbon-user.rank-third .user-avatar-container {
  width: 56rpx;
  height: 56rpx;
}

.carbon-user.rank-third .user-avatar {
  border: 2rpx solid #ff9800;
  box-shadow:
    0 0 15rpx rgba(255, 152, 0, 0.4),
    inset 0 1rpx 3rpx rgba(255, 255, 255, 0.6);
}

.carbon-user.rank-third .user-name {
  color: #bf360c;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
  font-size: 22rpx;
}

.carbon-user.rank-third .user-reward {
  background: linear-gradient(135deg, #f57c00 0%, #ff9800 50%, #ffab40 100%);
  box-shadow:
    0 4rpx 12rpx rgba(245, 124, 0, 0.3),
    inset 0 1rpx 2rpx rgba(255, 255, 255, 0.4);
  color: #ffffff;
  border: 2rpx solid #ffb74d;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
  font-size: 18rpx;
}

/* 排名徽章样式优化 */
.user-rank {
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  width: 26rpx;
  height: 26rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14rpx;
  font-weight: bold;
  box-shadow: 0 3rpx 10rpx rgba(0, 0, 0, 0.2);
  border: 2rpx solid #fff;
  z-index: 10;
  backdrop-filter: blur(10rpx);
}

.user-rank.rank-1 {
  background: linear-gradient(135deg, #ff8f00, #ffa726, #ffb74d);
  box-shadow:
    0 6rpx 20rpx rgba(255, 143, 0, 0.4),
    0 0 14rpx rgba(255, 143, 0, 0.3);
  border-color: #fff3e0;
}

.user-rank.rank-2 {
  background: linear-gradient(135deg, #607d8b, #90a4ae, #b0bec5);
  box-shadow:
    0 5rpx 16rpx rgba(96, 125, 139, 0.35),
    0 0 10rpx rgba(96, 125, 139, 0.25);
  border-color: #f5f5f5;
}

.user-rank.rank-3 {
  background: linear-gradient(135deg, #f57c00, #ff9800, #ffab40);
  box-shadow:
    0 4rpx 14rpx rgba(245, 124, 0, 0.35),
    0 0 8rpx rgba(245, 124, 0, 0.25);
  border-color: #fff3e0;
}

.rank-number {
  font-size: 20rpx;
  font-weight: bold;
  color: #fff;
  text-align: center;
  line-height: 1;
}







.carbon-user:active {
  transform: scale(0.96);
  transition: all 0.2s ease;
}

/* 调整第一名的点击效果 */
.carbon-user.rank-first:active {
  transform: scale(1.02) translateY(-3rpx);
}

.carbon-user.rank-second:active {
  transform: scale(0.98) translateY(3rpx);
}

.carbon-user.rank-third:active {
  transform: scale(0.98) translateY(3rpx);
}

.user-avatar-container {
  position: relative;
  width: 50rpx;
  height: 50rpx;
  margin-bottom: 12rpx;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-style: preserve-3d;
}

.user-avatar-container:active {
  transform: scale(1.1) rotateY(15deg);
}

.user-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2rpx solid #fff;
  box-shadow: 
    0 4rpx 16rpx rgba(0, 0, 0, 0.15),
    0 1rpx 6rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 2rpx rgba(255, 255, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  object-fit: cover;
  background: linear-gradient(135deg, #f5f5f5, #ffffff);
}

.user-avatar:hover {
  transform: scale(1.05) rotateZ(5deg);
  box-shadow: 
    0 6rpx 20rpx rgba(0, 0, 0, 0.2),
    0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.user-name {
  font-size: 22rpx;
  color: var(--text-color);
  margin-bottom: 5rpx;
  font-weight: 600;
  max-width: 120rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
  line-height: 1.3;
  letter-spacing: 0.3rpx;
  transition: all 0.3s ease;
  position: relative;
}

.user-name::after {
  content: '';
  position: absolute;
  bottom: -2rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2rpx;
  background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
  transition: width 0.3s ease;
}

.carbon-user:hover .user-name::after {
  width: 100%;
}

/* 添加排名显示 */
.user-rank-text {
  font-size: 18rpx;
  font-weight: 500;
  text-align: center;
  margin-bottom: 6rpx;
  letter-spacing: 0.2rpx;
  opacity: 0.8;
}

.carbon-user.rank-first .user-rank-text {
  color: #e65100;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.carbon-user.rank-second .user-rank-text {
  color: #424242;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.carbon-user.rank-third .user-rank-text {
  color: #bf360c;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.4);
}

.user-reward {
  font-size: 18rpx;
  color: #ffffff;
  font-weight: 600;
  background: var(--accent-gradient);
  padding: 8rpx 12rpx;
  border-radius: 16rpx;
  box-shadow: 
    0 3rpx 12rpx rgba(59, 127, 255, 0.25),
    0 1rpx 6rpx rgba(59, 127, 255, 0.15),
    inset 0 1rpx 2rpx rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  max-width: 120rpx;
  min-width: 64rpx;
  text-align: center;
  white-space: nowrap;
  text-overflow: ellipsis;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(20rpx);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  line-height: 1.3;
  letter-spacing: 0.3rpx;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
  transform-style: preserve-3d;
}

/* 悬浮光效 */
.user-reward::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
  z-index: 1;
}

.carbon-user:hover .user-reward::before,
.carbon-user:active .user-reward::before {
  left: 100%;
}

/* 外发光效果 */
.user-reward::after {
  content: '';
  position: absolute;
  top: -3rpx;
  left: -3rpx;
  right: -3rpx;
  bottom: -3rpx;
  background: inherit;
  border-radius: 22rpx;
  z-index: -1;
  opacity: 0.3;
  filter: blur(6rpx);
  animation: rewardGlow 3s ease-in-out infinite alternate;
  transform: scale(1.1);
}

@keyframes rewardGlow {
  0% { 
    opacity: 0.2; 
    transform: scale(1.05); 
    filter: blur(4rpx);
  }
  100% { 
    opacity: 0.5; 
    transform: scale(1.15); 
    filter: blur(8rpx);
  }
}

.user-reward:hover {
  transform: scale(1.05) translateY(-2rpx);
  box-shadow: 
    0 6rpx 24rpx rgba(59, 127, 255, 0.35),
    0 3rpx 12rpx rgba(59, 127, 255, 0.25),
    inset 0 1rpx 3rpx rgba(255, 255, 255, 0.3);
}

.user-reward:active {
  transform: scale(0.98) translateY(1rpx);
}

/* 碳豆排行榜空状态优化 */
.carbon-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 38rpx 32rpx;
  text-align: center;
  position: relative;
  z-index: 1;
  background: linear-gradient(135deg, rgba(249, 250, 251, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%);
  border-radius: 20rpx;
  margin: 0 16rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(0, 0, 0, 0.04);
}

.carbon-empty-state::before {
  content: '';
  position: absolute;
  top: -20rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(90deg, transparent, rgba(59, 127, 255, 0.3), transparent);
  border-radius: 2rpx;
}

.carbon-empty-state .van-icon {
  opacity: 0.6;
  animation: float 3s ease-in-out infinite;
}

.carbon-empty-state .empty-text {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-top: 14rpx;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}

.carbon-empty-state .empty-subtext {
  font-size: 20rpx;
  color: var(--text-light);
  margin-top: 6rpx;
  line-height: 1.5;
  opacity: 0.8;
}

/* 分类标签 */
.category-tabs {
  display: flex;
  justify-content: space-between;
  margin: 0 8rpx 0 8rpx;
  padding: 14rpx 20rpx 12rpx 20rpx;
  background: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  box-shadow: none;
  border: 1rpx solid rgba(59, 127, 255, 0.08);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 1;
}

.tab {
  font-size: 26rpx;
  color: var(--text-secondary);
  padding: 8rpx 12rpx;
  transition: var(--transition);
  position: relative;
  display: flex;
  align-items: center;
  border-radius: 12rpx;
  font-weight: 400;
  min-height: 32rpx;
}

.tab.active {
  color: #333333;
  font-weight: bold;
  background: rgba(59, 127, 255, 0.05);
  transform: none;
}

.tab.active::after {
  display: none;
}

.tab van-icon {
  margin-left: 6rpx;
  opacity: 0.8;
}

.tab.active van-icon {
  color: #333333 !important;
  opacity: 1;
}

/* 商品列表容器 */
.swap-items-container {
  padding: 16rpx 8rpx 24rpx 8rpx;
  width: calc(100% - 16rpx);
  background: #ffffff;
  margin: 0 8rpx 20rpx 8rpx;
  border-radius: 0 0 20rpx 20rpx;
  box-shadow: var(--card-shadow);
  border: 1rpx solid rgba(59, 127, 255, 0.08);
  border-top: none;
  box-sizing: border-box;
  position: relative;
  z-index: 0;
}

/* 商品列表 */
.swap-items {
  padding: 0;
  box-sizing: border-box;
  display: flex;
  width: 100%;
}

/* 瀑布流列样式 */
.swap-item-column {
  width: 49%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.left-column {
  margin-right: 1%;
}

.right-column {
  margin-left: 1%;
}

.swap-item {
  width: 100%;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
  transition: var(--transition);
  border: 1rpx solid rgba(0, 0, 0, 0.04);
  position: relative;
  display: flex;
  flex-direction: column;
  opacity: 0;
  animation: slideInUp 0.6s ease-out forwards;
}

.swap-item:active {
  transform: translateY(-4rpx) scale(0.98);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.12);
}

.image-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
}

.item-image {
  width: 100%;
  display: block;
  transition: var(--transition);
  border-radius: 16rpx 16rpx 0 0;
  background-color: #f8f9fa;
}

.item-image:active {
  transform: scale(1.02);
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.2), transparent);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition);
}

.swap-item:active .image-overlay {
  opacity: 1;
}

.image-overlay text {
  color: #ffffff;
  font-size: 26rpx;
  font-weight: 500;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.6);
}

.item-info {
  padding: 18rpx 16rpx 14rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  position: relative;
}

.item-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 16rpx;
  right: 16rpx;
  height: 1rpx;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.06), transparent);
}

.item-description {
  font-size: 28rpx;
  color: var(--text-color);
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  min-height: 40rpx;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
  padding-bottom: 12rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.04);
}

.item-price {
  font-size: 32rpx;
  color: #ff4757;
  font-weight: bold;
  text-shadow: 0 1rpx 2rpx rgba(255, 71, 87, 0.1);
}

/* 添加发布者信息行样式 */
.item-publisher-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0;
  padding-top: 0;
}

/* 添加发布者信息区域的样式 */
.item-publisher {
  display: flex;
  align-items: center;
  flex: 1;
  margin-right: 16rpx;
}

.publisher-avatar {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

.publisher-name {
  font-size: 24rpx;
  color: var(--text-secondary);
  max-width: 120rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 400;
}

.item-time {
  font-size: 22rpx;
  color: var(--text-light);
  display: flex;
  align-items: center;
  font-weight: 400;
  white-space: nowrap;
}

.item-tag {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background: var(--accent-gradient);
  color: #ffffff;
  font-size: 22rpx;
  font-weight: 600;
  padding: 6rpx 14rpx;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 8rpx rgba(59, 127, 255, 0.3);
  z-index: 2;
  backdrop-filter: blur(10rpx);
}

/* 小区名称标签样式 */
.residential-badge {
  position: absolute;
  bottom: 12rpx;
  left: 12rpx;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10rpx);
  border-radius: 12rpx;
  padding: 4rpx 8rpx;
  z-index: 3;
  max-width: 200rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 32rpx;
  gap: 4rpx;
}

.residential-text {
  font-size: 18rpx;
  color: #ffffff;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1;
}

/* 加载状态样式 */
.loading-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
  background: rgba(59, 127, 255, 0.03);
  border-radius: 20rpx;
  margin: 20rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(59, 127, 255, 0.2);
  border-top: 4rpx solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 26rpx;
  color: var(--text-secondary);
  font-weight: 500;
}

/* 底部加载更多提示 */
.bottom-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60rpx;
  margin: 10rpx 0 20rpx;
}

/* 预加载提示样式 */
.preload-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80rpx;
  margin: 10rpx 0 20rpx;
  background: rgba(59, 127, 255, 0.05);
  border-radius: 16rpx;
  border: 1rpx solid rgba(59, 127, 255, 0.1);
}

.preload-text {
  margin-left: 16rpx;
  font-size: 24rpx;
  color: var(--accent-color);
  font-weight: 500;
}

/* 加载更多按钮样式 */
.load-more-button {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 70rpx;
  margin: 16rpx 0;
  background-color: #f5f5f5;
  color: #666;
  font-size: 26rpx;
  border-radius: 35rpx;
  width: 50%;
  margin: 16rpx auto;
}

.load-more-button:active {
  background-color: #e9e9e9;
  opacity: 0.9;
}

.loading-dot {
  width: 12rpx;
  height: 12rpx;
  background-color: var(--accent-color);
  border-radius: 50%;
  margin: 0 6rpx;
  opacity: 0.7;
  animation: bounce 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

.loading-dot:nth-child(3) {
  animation-delay: 0s;
}

.no-more-data {
  width: 100%;
  text-align: center;
  padding: 20rpx 0;
  font-size: 24rpx;
  color: var(--text-light);
  background: rgba(0, 0, 0, 0.02);
  border-radius: 16rpx;
  margin: 10rpx 0;
}

.empty-state {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
  background: rgba(59, 127, 255, 0.03);
  border-radius: 20rpx;
  margin: 20rpx 0;
}

.empty-state text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: var(--text-secondary);
  font-weight: 500;
}

/* 骨架屏样式 */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 8rpx;
}

.skeleton-grid {
  display: flex;
  width: 100%;
  gap: 24rpx;
  padding: 0 24rpx;
}

.skeleton-item-column {
  width: 48%;
  display: flex;
  flex-direction: column;
}

.skeleton-item {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
}

.skeleton-image {
  width: 100%;
  height: 300rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 16rpx 16rpx 0 0;
}

.skeleton-content {
  padding: 24rpx 20rpx;
}

.skeleton-line {
  height: 28rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 14rpx;
  margin-bottom: 16rpx;
}

.skeleton-line.long {
  width: 100%;
  height: 32rpx;
}

.skeleton-line.medium {
  width: 75%;
  height: 28rpx;
}

.skeleton-line.short {
  width: 50%;
  height: 24rpx;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes bounce {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(1.05);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 给不同位置的卡片设置不同的动画延迟 */
.left-column .swap-item:nth-child(1) { animation-delay: 0.1s; }
.left-column .swap-item:nth-child(2) { animation-delay: 0.3s; }
.left-column .swap-item:nth-child(3) { animation-delay: 0.5s; }
.right-column .swap-item:nth-child(1) { animation-delay: 0.2s; }
.right-column .swap-item:nth-child(2) { animation-delay: 0.4s; }
.right-column .swap-item:nth-child(3) { animation-delay: 0.6s; }
